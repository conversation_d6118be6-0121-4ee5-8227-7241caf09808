apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: chhoto-url
spec:
  replicas: 1
  selector:
    matchLabels:
      app: chhoto-url
  template:
    metadata:
      labels:
        app: chhoto-url
    spec:
      containers:
        - name: chhoto-url
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          ports:
            - containerPort: 4567
          env:
            - name: password
              valueFrom:
                secretKeyRef:
                  name: secret
                  key: password
            {{- if .Values.api_key }}
            - name: api_key
              valueFrom:
                secretKeyRef:
                  name: secret
                  key: api_key
            {{- end }}
            {{- if .Values.hash_algorithm }}
            - name: hash_algorithm 
              value: {{ .Values.hash_algorithm  }}
            {{- end }}
            - name: db_url
              value: /db/urls.sqlite
            - name: site_url
              value: "{{ .Values.protocol }}://{{ .Values.fqdn }}"
            - name: redirect_method
              value: {{ .Values.redirect_method }}
            - name: slug_style
              value: {{ .Values.slug_style }}
            - name: slug_length
              value: "{{ .Values.slug_length }}"
            - name: try_longer_slug
              value: "{{ .Values.try_longer_slug }}"
            - name: public_mode
              value: {{ .Values.public_mode }}
            - name: public_mode_expiry_delay
              value: {{ .Values.public_mode_expiry_delay }}
            - name: disable_frontend
              value: {{ .Values.disable_frontend }}
            - name: allow_capital_letters 
              value: {{ .Values.allow_capital_letters }}
            {{- if .Values.custom_landing_directory }}
            - name: custom_landing_directory
              value: {{ .Values.custom_landing_directory }}
            {{- end }}
            {{- if .Values.cache_control_header }}
            - name: cache_control_header
              value: {{ .Values.cache_control_header }}
            {{- end }}
          volumeMounts:
            - name: data
              mountPath: /db
  volumeClaimTemplates:
    - metadata:
        name: data
      spec:
        accessModes: [ "ReadWriteOnce" ]
        resources:
          requests:
            storage: 100Mi
