name: Tests

on:
  push:
    branches: [ "main" ]
    paths: [ "actix/**" ]
  pull_request:
    branches: [ "main" ]
    paths: [ "actix/**" ]
  workflow_dispatch:

env:
  CARGO_TERM_COLOR: always

jobs:
  test:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./actix
    steps:
    - uses: actions/checkout@v4
    - name: Cache Dependencies
      uses: actions/cache@v4
      with:
        path: |
          actix/target
          actix/.cargo
        key: cargo-${{ runner.os }}-${{ hashFiles('actix/Cargo.lock') }}
        restore-keys: |
            cargo-${{ runner.os }}-
    - name: Build
      run: cargo build
    - name: Run tests
      run: cargo test
