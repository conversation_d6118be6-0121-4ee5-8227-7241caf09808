# SPDX-FileCopyrightText: 2023 <PERSON><PERSON><PERSON> <<EMAIL>>
# SPDX-License-Identifier: MIT

[package]
name = "chhoto-url"
version = "6.2.1"
edition = "2021"
authors = ["<PERSON><PERSON><PERSON> <sayantan[dot]santra689[at]gmail[dot]com"]
license = "mit"
description = "A simple selfhosted URL shortener with no unnecessary features."
homepage = "https://github.com/SinTan1729/chhoto-url"
documentation = "https://github.com/SinTan1729/chhoto-url"
repository = "https://github.com/SinTan1729/chhoto-url"
readme = "README.md"
keywords = [
    "docker",
    "rust",
    "self-hosted",
    "url-shortener",
    "webapp",
    "shortener",
    "link-shortener",
    "actix-web",
]
categories = ["web-programming"]

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
actix-web = "4.5.1"
actix-files = "0.6.5"
rusqlite = { version = "0.36.0", features = [ "bundled" ] }
regex = "1.10.3"
rand = "0.9.0"
passwords = "3.1.16"
actix-session = { version = "0.10.0", features = [ "cookie-session" ] }
nanoid = "0.4.0"
serde_json = "1.0.115"
serde = { version = "1.0.197", features = [ "derive" ] }
argon2 = "0.5.3"
chrono = "0.4.41"
tokio = "1.44.2"
log = "0.4.27"
env_logger = "0.11.8"

[dev-dependencies]
actix-http = "3.11.0"
actix-service = "2.0.3"
regex = "1.10.3"
